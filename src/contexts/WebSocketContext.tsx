'use client'

import React, { createContext, useContext, useEffect, useRef, useState, useCallback, ReactNode } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useSession } from 'next-auth/react'

interface WebSocketMessage {
  type: string
  channel?: string
  data: any
  timestamp: string
}

interface WebSocketContextType {
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  subscribe: (channel: string, callback?: (data: any) => void) => void
  unsubscribe: (channel: string, callback?: (data: any) => void) => void
  sendMessage: (message: any) => void
  lastMessage: WebSocketMessage | null
  connectionStats: {
    reconnectAttempts: number
    messagesReceived: number
    messagesSent: number
    uptime: number
  }
  reconnect: () => void
  disconnect: () => void
}

interface WebSocketProviderProps {
  children: ReactNode
  wsUrl?: string
  autoConnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
}

const WebSocketContext = createContext<WebSocketContextType | null>(null)

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({
  children,
  wsUrl,
  autoConnect = true,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5
}) => {
  const { data: session } = useSession()
  const queryClient = useQueryClient()
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const connectionTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const subscriptionsRef = useRef<Set<string>>(new Set())
  const subscribersRef = useRef<Map<string, Set<(data: any) => void>>>(new Map())
  const messageQueueRef = useRef<any[]>([])

  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  const [connectionStats, setConnectionStats] = useState({
    reconnectAttempts: 0,
    messagesReceived: 0,
    messagesSent: 0,
    uptime: 0
  })

  const connectStartTime = useRef<number>(0)

  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'broadcast':
        if (message.channel?.startsWith('product:')) {
          const productId = message.channel.split(':')[1]

          if (message.data.type === 'new_bid') {
            // Invalidate product queries for real-time bid updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['bidding', 'history', productId]
            })
          } else if (message.data.type === 'auction_extended') {
            // Invalidate product queries for auction extension updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'logs', productId]
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'stats', productId]
            })
          }
        }
        break

      case 'user_message':
        if (message.data.type === 'auto_bid_executed') {
          const productId = message.data.productId
          queryClient.invalidateQueries({
            queryKey: ['products', 'detail', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['bidding', 'history', productId]
          })
          queryClient.invalidateQueries({
            queryKey: ['auto-bid', productId]
          })
        }
        break
    }
  }, [queryClient])

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    // Prevent multiple connection attempts
    if (wsRef.current?.readyState === WebSocket.CONNECTING) {
      return
    }

    const url = wsUrl || process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'

    try {
      setConnectionStatus('connecting')
      wsRef.current = new WebSocket(url)
      connectStartTime.current = Date.now()

      // Set connection timeout with retry logic
      connectionTimeoutRef.current = setTimeout(() => {
        if (wsRef.current?.readyState === WebSocket.CONNECTING) {
          console.warn('WebSocket connection timeout')
          wsRef.current?.close()

          // Retry connection if under max attempts
          if (connectionStats.reconnectAttempts < maxReconnectAttempts) {
            const delay = Math.min(2000 * Math.pow(1.5, connectionStats.reconnectAttempts), 15000)
            console.log(`Connection timeout, retrying in ${delay}ms (attempt ${connectionStats.reconnectAttempts + 1})`)

            setConnectionStats(prev => ({
              ...prev,
              reconnectAttempts: prev.reconnectAttempts + 1
            }))

            reconnectTimeoutRef.current = setTimeout(() => {
              connect()
            }, delay)
          } else {
            setConnectionStatus('error')
            console.error('Connection timeout - max attempts reached')
          }
        }
      }, 8000) // 8 second timeout

      wsRef.current.onopen = () => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }
        console.log('WebSocket connected successfully')
        setIsConnected(true)
        setConnectionStatus('connected')
        setConnectionStats(prev => ({
          ...prev,
          reconnectAttempts: 0,
          uptime: Date.now() - connectStartTime.current
        }))

        // Authenticate if user is logged in
        if (session?.user?.id) {
          wsRef.current?.send(JSON.stringify({
            type: 'authenticate',
            userId: session.user.id,
            token: session.accessToken || 'temp-token',
            sessionId: session.user.id
          }))
        }

        // Re-subscribe to all channels
        subscriptionsRef.current.forEach(channel => {
          wsRef.current?.send(JSON.stringify({
            type: 'subscribe',
            channel
          }))
        })

        // Send queued messages
        while (messageQueueRef.current.length > 0) {
          const queuedMessage = messageQueueRef.current.shift()
          wsRef.current?.send(JSON.stringify(queuedMessage))
        }
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          setLastMessage(message)
          setConnectionStats(prev => ({
            ...prev,
            messagesReceived: prev.messagesReceived + 1,
            uptime: Date.now() - connectStartTime.current
          }))

          // Handle different message types
          handleWebSocketMessage(message)

          // Handle channel-specific subscribers
          if (message.type === 'broadcast' && message.channel) {
            const channelSubscribers = subscribersRef.current.get(message.channel) || new Set()
            channelSubscribers.forEach(callback => {
              try {
                callback(message.data)
              } catch (error) {
                console.error('Error in subscriber callback:', error)
              }
            })
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      wsRef.current.onclose = (event) => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }

        console.log(`WebSocket disconnected ${event.code}`)
        setIsConnected(false)
        setConnectionStatus('disconnected')

        // Handle different close codes
        if (event.code === 1000) {
          // Normal closure - don't reconnect
          console.log('WebSocket closed normally')
          return
        }

        if (event.code === 1006 || event.code === 1005) {
          // Abnormal closure - attempt reconnection with backoff
          if (connectionStats.reconnectAttempts < maxReconnectAttempts) {
            const delay = Math.min(3000 * Math.pow(1.8, connectionStats.reconnectAttempts), 30000)
            console.log(`Connection lost (${event.code}), reconnecting in ${delay}ms (attempt ${connectionStats.reconnectAttempts + 1})`)

            setConnectionStats(prev => ({
              ...prev,
              reconnectAttempts: prev.reconnectAttempts + 1
            }))

            reconnectTimeoutRef.current = setTimeout(() => {
              connect()
            }, delay)
          } else {
            console.log('Max reconnection attempts reached')
            setConnectionStatus('error')
          }
        }
      }

      wsRef.current.onerror = (error) => {
        // Clear connection timeout
        if (connectionTimeoutRef.current) {
          clearTimeout(connectionTimeoutRef.current)
          connectionTimeoutRef.current = undefined
        }

        console.error('WebSocket error:', error)

        // Don't immediately set error state, let onclose handle reconnection
        if (wsRef.current?.readyState === WebSocket.CONNECTING) {
          console.log('Connection failed - will retry via onclose handler')
        }
      }
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setConnectionStatus('error')
    }
  }, [session, wsUrl, maxReconnectAttempts, connectionStats.reconnectAttempts, handleWebSocketMessage])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = undefined
    }

    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current)
      connectionTimeoutRef.current = undefined
    }

    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }

    setIsConnected(false)
    setConnectionStatus('disconnected')
  }, [])

  const subscribe = useCallback((channel: string) => {
    subscriptionsRef.current.add(channel)
    
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'subscribe',
        channel
      }))
    }
  }, [])

  const unsubscribe = useCallback((channel: string) => {
    subscriptionsRef.current.delete(channel)
    
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'unsubscribe',
        channel
      }))
    }
  }, [])

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
      setConnectionStats(prev => ({
        ...prev,
        messagesSent: prev.messagesSent + 1
      }))
    } else {
      // Queue message for when connection is restored
      messageQueueRef.current.push(message)
      console.warn('WebSocket is not connected, message queued')
    }
  }, [])

  useEffect(() => {
    if (autoConnect) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [autoConnect, connect, disconnect])

  // Update uptime periodically
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(() => {
        setConnectionStats(prev => ({
          ...prev,
          uptime: Date.now() - connectStartTime.current
        }))
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [isConnected])

  const reconnect = useCallback(() => {
    // Reset reconnection attempts and force reconnect
    setConnectionStats(prev => ({ ...prev, reconnectAttempts: 0 }))
    disconnect()
    setTimeout(() => connect(), 100)
  }, [connect, disconnect])

  const value: WebSocketContextType = {
    isConnected,
    connectionStatus,
    subscribe,
    unsubscribe,
    sendMessage,
    lastMessage,
    connectionStats,
    reconnect,
    disconnect
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}

export const useWebSocketContext = () => {
  const context = useContext(WebSocketContext)
  return context // Return null if not within provider instead of throwing
}

export const useWebSocketContextRequired = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocketContextRequired must be used within a WebSocketProvider')
  }
  return context
}
