'use client'

import React from 'react'
import { Container, VStack, Heading, Text, Box } from '@chakra-ui/react'
import WebSocketConnectionTest from '@/components/debug/WebSocketConnectionTest'
import WebSocketStatus from '@/components/debug/WebSocketStatus'

export default function WebSocketDebugPage() {
  return (
    <Container maxW="4xl" py={8}>
      <VStack gap={6} align="stretch">
        <Box>
          <Heading size="xl" mb={2}>WebSocket Debug</Heading>
          <Text color="gray.600">
            Test and debug WebSocket connections to identify and resolve connection issues.
          </Text>
        </Box>

        <Box>
          <Heading size="md" mb={4}>Current WebSocket Status</Heading>
          <WebSocketStatus showDetails={true} position="relative" showReconnectButton={true} />
        </Box>

        <Box>
          <Heading size="md" mb={4}>Connection Test</Heading>
          <WebSocketConnectionTest />
        </Box>

        <Box p={4} bg="blue.50" border="1px solid" borderColor="blue.200" borderRadius="md">
          <Heading size="sm" mb={2}>Troubleshooting Tips</Heading>
          <VStack align="stretch" gap={2} fontSize="sm">
            <Text>• Check if the WebSocket server is running on port 3001</Text>
            <Text>• Verify the NEXT_PUBLIC_WS_URL environment variable</Text>
            <Text>• Check browser console for detailed error messages</Text>
            <Text>• Ensure firewall/network allows WebSocket connections</Text>
            <Text>• Try refreshing the page if connection fails</Text>
          </VStack>
        </Box>
      </VStack>
    </Container>
  )
}
