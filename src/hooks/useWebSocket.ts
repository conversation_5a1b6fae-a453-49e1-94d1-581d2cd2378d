'use client'

import { useEffect, useRef, useCallback, useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useWebSocketContext } from '../contexts/WebSocketContext'

interface WebSocketMessage {
  type: string
  channel?: string
  data: any
  timestamp: string
}

interface UseWebSocketOptions {
  productId?: string
  userId?: string
  onMessage?: (message: WebSocketMessage) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
  useContext?: boolean // New option to use context or create individual connection
}

interface WebSocketHookReturn {
  isConnected: boolean
  sendMessage: (message: any) => void
  connect: () => void
  disconnect: () => void
  connectionStatus?: 'connecting' | 'connected' | 'disconnected' | 'error'
  stats?: {
    reconnectAttempts: number
    messagesReceived: number
    messagesSent: number
    uptime: number
  }
}

export const useWebSocket = (options: UseWebSocketOptions = {}): WebSocketHookReturn => {
  const {
    productId,
    userId,
    onMessage,
    onConnect,
    onDisconnect,
    onError,
    useContext = true
  } = options

  // Always call the context hook (required by React rules)
  const context = useWebSocketContext()

  // Determine if we should use context or individual connection
  const shouldUseContext = useContext && context !== null

  // Individual connection state (fallback)
  const wsRef = useRef<WebSocket | null>(null)
  const queryClient = useQueryClient()
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5
  const [individualConnectionStatus, setIndividualConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [individualStats, setIndividualStats] = useState({
    reconnectAttempts: 0,
    messagesReceived: 0,
    messagesSent: 0,
    uptime: 0
  })

  // Shared message handling logic
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'broadcast':
        if (message.channel?.startsWith('product:')) {
          const productIdFromChannel = message.channel.split(':')[1]

          if (message.data.type === 'new_bid') {
            // Invalidate product queries for real-time bid updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productIdFromChannel]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['bidding', 'history', productIdFromChannel]
            })
          } else if (message.data.type === 'auction_extended') {
            // Invalidate product queries for auction extension updates
            queryClient.invalidateQueries({
              queryKey: ['products', 'detail', productIdFromChannel]
            })
            queryClient.invalidateQueries({
              queryKey: ['products', 'slug']
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'logs', productIdFromChannel]
            })
            queryClient.invalidateQueries({
              queryKey: ['auction-extension', 'stats', productIdFromChannel]
            })
          }
        }
        break

      case 'user_message':
        if (message.data.type === 'auto_bid_executed') {
          // Invalidate queries when auto-bid is executed
          const productIdFromMessage = message.data.productId
          queryClient.invalidateQueries({
            queryKey: ['products', 'detail', productIdFromMessage]
          })
          queryClient.invalidateQueries({
            queryKey: ['products', 'slug']
          })
          queryClient.invalidateQueries({
            queryKey: ['bidding', 'history', productIdFromMessage]
          })
          queryClient.invalidateQueries({
            queryKey: ['auto-bid', productIdFromMessage]
          })
        }
        break
    }
  }, [queryClient])

  // Enhanced connection logic with context support
  const connect = useCallback(() => {
    // If using context, just subscribe to channels
    if (shouldUseContext && context) {
      if (productId) {
        context.subscribe(`product:${productId}`)
      }
      if (userId) {
        context.subscribe(`user:${userId}`)
      }
      onConnect?.()
      return
    }

    // Individual connection fallback
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    try {
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'
      setIndividualConnectionStatus('connecting')
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        console.log('Individual WebSocket connected')
        reconnectAttempts.current = 0
        setIndividualConnectionStatus('connected')
        setIndividualStats(prev => ({ ...prev, reconnectAttempts: 0 }))
        onConnect?.()

        // Subscribe to product updates if productId is provided
        if (productId) {
          wsRef.current?.send(JSON.stringify({
            type: 'subscribe',
            channel: `product:${productId}`
          }))
        }

        // Subscribe to user updates if userId is provided
        if (userId) {
          wsRef.current?.send(JSON.stringify({
            type: 'subscribe',
            channel: `user:${userId}`
          }))
        }
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          console.log('Individual WebSocket message received:', message)

          setIndividualStats(prev => ({
            ...prev,
            messagesReceived: prev.messagesReceived + 1
          }))

          // Handle different message types (same logic as context)
          handleWebSocketMessage(message)
          onMessage?.(message)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      wsRef.current.onclose = () => {
        console.log('Individual WebSocket disconnected')
        setIndividualConnectionStatus('disconnected')
        onDisconnect?.()

        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000)
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts.current})`)

          setIndividualStats(prev => ({
            ...prev,
            reconnectAttempts: prev.reconnectAttempts + 1
          }))

          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, delay)
        } else {
          console.error('Max reconnection attempts reached')
          setIndividualConnectionStatus('error')
        }
      }

      wsRef.current.onerror = (error) => {
        console.error('Individual WebSocket error:', error)
        setIndividualConnectionStatus('error')
        onError?.(error)
      }
    } catch (error) {
      console.error('Failed to create individual WebSocket connection:', error)
      setIndividualConnectionStatus('error')
    }
  }, [productId, userId, onConnect, onDisconnect, onMessage, onError, context, handleWebSocketMessage, shouldUseContext])

  const disconnect = useCallback(() => {
    // If using context, unsubscribe from channels
    if (shouldUseContext && context) {
      if (productId) {
        context.unsubscribe(`product:${productId}`)
      }
      if (userId) {
        context.unsubscribe(`user:${userId}`)
      }
      return
    }

    // Individual connection cleanup
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
    setIndividualConnectionStatus('disconnected')
  }, [context, productId, userId, shouldUseContext])

  const sendMessage = useCallback((message: any) => {
    // Use context if available
    if (shouldUseContext && context) {
      context.sendMessage(message)
      return
    }

    // Individual connection fallback
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
      setIndividualStats(prev => ({
        ...prev,
        messagesSent: prev.messagesSent + 1
      }))
    } else {
      console.warn('WebSocket is not connected')
    }
  }, [context, shouldUseContext])

  useEffect(() => {
    connect()

    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  // Return appropriate state based on context availability
  const isConnected = shouldUseContext && context
    ? context.isConnected
    : wsRef.current?.readyState === WebSocket.OPEN

  const connectionStatus = shouldUseContext && context
    ? context.connectionStatus
    : individualConnectionStatus

  const stats = shouldUseContext && context
    ? context.connectionStats
    : individualStats

  return {
    isConnected,
    sendMessage,
    connect,
    disconnect,
    connectionStatus,
    stats
  }
}

// Hook specifically for product real-time updates
export const useProductWebSocket = (productId: string) => {
  return useWebSocket({
    productId,
    useContext: true, // Always use context for product updates
    onMessage: (message) => {
      // Additional product-specific message handling can be added here
      console.log('Product WebSocket message:', message)
    }
  })
}

// Hook specifically for user real-time updates
export const useUserWebSocket = (userId: string) => {
  return useWebSocket({
    userId,
    onMessage: (message) => {
      // Additional user-specific message handling can be added here
      console.log('User WebSocket message:', message)
    }
  })
}
