'use client'

import React, { useState, useRef, useCallback } from 'react'
import { Box, Button, Text, VStack, HStack, Badge, Textarea } from '@chakra-ui/react'

const WebSocketConnectionTest: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected')
  const [messages, setMessages] = useState<string[]>([])
  const [lastError, setLastError] = useState<string | null>(null)
  const wsRef = useRef<WebSocket | null>(null)

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'
    setConnectionStatus('connecting')
    setLastError(null)
    
    try {
      wsRef.current = new WebSocket(wsUrl)

      wsRef.current.onopen = () => {
        console.log('Test WebSocket connected')
        setConnectionStatus('connected')
        setMessages(prev => [...prev, `[${new Date().toLocaleTimeString()}] Connected to ${wsUrl}`])
      }

      wsRef.current.onmessage = (event) => {
        console.log('Test WebSocket message:', event.data)
        setMessages(prev => [...prev, `[${new Date().toLocaleTimeString()}] Received: ${event.data}`])
      }

      wsRef.current.onclose = (event) => {
        console.log('Test WebSocket closed:', event.code, event.reason)
        setConnectionStatus('disconnected')
        setMessages(prev => [...prev, `[${new Date().toLocaleTimeString()}] Disconnected: ${event.code} - ${event.reason || 'No reason'}`])
      }

      wsRef.current.onerror = (error) => {
        console.error('Test WebSocket error:', error)
        setConnectionStatus('error')
        setLastError('Connection failed - check if WebSocket server is running')
        setMessages(prev => [...prev, `[${new Date().toLocaleTimeString()}] Error: Connection failed`])
      }
    } catch (error) {
      console.error('Failed to create WebSocket:', error)
      setConnectionStatus('error')
      setLastError(error instanceof Error ? error.message : 'Unknown error')
    }
  }, [])

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
    setConnectionStatus('disconnected')
  }, [])

  const sendTestMessage = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const testMessage = {
        type: 'ping',
        timestamp: new Date().toISOString()
      }
      wsRef.current.send(JSON.stringify(testMessage))
      setMessages(prev => [...prev, `[${new Date().toLocaleTimeString()}] Sent: ${JSON.stringify(testMessage)}`])
    }
  }, [])

  const clearMessages = useCallback(() => {
    setMessages([])
    setLastError(null)
  }, [])

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'green'
      case 'connecting': return 'yellow'
      case 'error': return 'red'
      case 'disconnected': return 'gray'
      default: return 'gray'
    }
  }

  return (
    <Box p={4} border="1px solid" borderColor="gray.200" borderRadius="md">
      <VStack align="stretch" gap={4}>
        <HStack justify="space-between">
          <Text fontSize="lg" fontWeight="bold">WebSocket Connection Test</Text>
          <Badge colorScheme={getStatusColor()}>
            {connectionStatus.toUpperCase()}
          </Badge>
        </HStack>

        <Text fontSize="sm" color="gray.600">
          Testing connection to: {process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001'}
        </Text>

        {lastError && (
          <Box p={2} bg="red.50" border="1px solid" borderColor="red.200" borderRadius="md">
            <Text fontSize="sm" color="red.600">{lastError}</Text>
          </Box>
        )}

        <HStack>
          <Button 
            onClick={connect} 
            disabled={connectionStatus === 'connected' || connectionStatus === 'connecting'}
            colorScheme="blue"
            size="sm"
          >
            Connect
          </Button>
          <Button 
            onClick={disconnect} 
            disabled={connectionStatus === 'disconnected'}
            colorScheme="red"
            size="sm"
          >
            Disconnect
          </Button>
          <Button 
            onClick={sendTestMessage} 
            disabled={connectionStatus !== 'connected'}
            colorScheme="green"
            size="sm"
          >
            Send Test Message
          </Button>
          <Button 
            onClick={clearMessages} 
            variant="outline"
            size="sm"
          >
            Clear Log
          </Button>
        </HStack>

        <Box>
          <Text fontSize="sm" fontWeight="medium" mb={2}>Connection Log:</Text>
          <Textarea
            value={messages.join('\n')}
            readOnly
            placeholder="Connection events will appear here..."
            minH="200px"
            fontSize="xs"
            fontFamily="mono"
          />
        </Box>
      </VStack>
    </Box>
  )
}

export default WebSocketConnectionTest
