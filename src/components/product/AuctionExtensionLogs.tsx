import React, { useEffect, useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Spinner,
  Icon,
  Button,
  useDisclosure,
  Card,
  CardBody,
  CardHeader,
  Heading,
  SimpleGrid,
  Separator
} from '@chakra-ui/react';
import { Fa<PERSON>lock, FaUser, FaRobot, FaChevronDown, FaChevronUp, FaHistory, FaWifi } from 'react-icons/fa';
import { MdSignalWifiOff } from 'react-icons/md';
import { useProductExtensionLogsQuery, useProductExtensionStatsQuery } from '../../services/useAuctionExtensionQuery';
import { formatDistanceToNow, format } from 'date-fns';
import { Alert } from '../ui/alert';
import { useProductWebSocket } from '../../hooks/useWebSocket';

interface AuctionExtensionLogsProps {
  productId: string;
}

const AuctionExtensionLogs: React.FC<AuctionExtensionLogsProps> = ({ productId }) => {
  const { open, onOpen, onClose } = useDisclosure();
  const [realtimeUpdates, setRealtimeUpdates] = useState(0); // Counter to trigger re-renders
  const [lastExtensionTime, setLastExtensionTime] = useState<Date | null>(null);

  const {
    data: extensionLogs,
    isLoading: logsLoading,
    error: logsError,
    refetch: refetchLogs
  } = useProductExtensionLogsQuery(productId);

  const {
    data: extensionStats,
    isLoading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useProductExtensionStatsQuery(productId);

  // WebSocket integration for real-time updates
  const { isConnected, connectionStatus } = useProductWebSocket(productId);

  // Handle real-time auction extension updates
  useEffect(() => {
    const handleExtensionUpdate = () => {
      console.log('🔄 Auction extension detected, refreshing data...');
      setRealtimeUpdates(prev => prev + 1);
      setLastExtensionTime(new Date());

      // Refetch both logs and stats
      refetchLogs();
      refetchStats();
    };

    // Listen for auction extension events
    // This would be triggered by the WebSocket message handling in the hook
    // The useProductWebSocket hook already handles query invalidation
    // but we can add additional UI feedback here

    return () => {
      // Cleanup if needed
    };
  }, [productId, refetchLogs, refetchStats]);

  // Fade out the highlight effect after 3 seconds
  useEffect(() => {
    if (realtimeUpdates > 0) {
      const timer = setTimeout(() => {
        setRealtimeUpdates(0);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [realtimeUpdates]);

  if (logsLoading || statsLoading) {
    return (
      <Card.Root>
        <CardBody>
          <HStack justify="center" py={4}>
            <Spinner size="sm" />
            <Text>Loading extension logs...</Text>
          </HStack>
        </CardBody>
      </Card.Root>
    );
  }

  if (logsError || statsError) {
    return (
      <Alert status="error">
        <Alert.Icon />
        Failed to load auction extension data
      </Alert>
    );
  }

  if (!extensionLogs || extensionLogs.totalExtensions === 0) {
    return (
      <Card.Root>
        <CardBody>
          <Text color="gray.500" textAlign="center" py={4}>
            No auction extensions have occurred for this product.
          </Text>
        </CardBody>
      </Card.Root>
    );
  }

  return (
    <Card.Root>
      <CardHeader>
        <HStack justify="space-between" align="center">
          <HStack>
            <Icon as={FaHistory} color="blue.500" />
            <Heading size="md">Auction Extensions</Heading>
            <Badge colorScheme="blue" variant="subtle">
              {extensionLogs.totalExtensions} total
            </Badge>
            {/* Real-time connection indicator */}
            <Badge
              colorScheme={isConnected ? 'green' : 'red'}
              variant="subtle"
              fontSize="xs"
            >
              <Icon
                as={isConnected ? FaWifi : MdSignalWifiOff}
                boxSize={2}
                mr={1}
              />
              {isConnected ? 'Live' : 'Offline'}
            </Badge>
            {lastExtensionTime && (
              <Badge colorScheme="orange" variant="subtle" fontSize="xs">
                Updated {formatDistanceToNow(lastExtensionTime, { addSuffix: true })}
              </Badge>
            )}
          </HStack>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              open ? onClose() : onOpen();
            }}
          >
            <Icon as={open ? FaChevronUp : FaChevronDown} />
            {open ? 'Hide' : 'Show'} Details
          </Button>
        </HStack>
      </CardHeader>

      <CardBody>
        {extensionStats && (
          <SimpleGrid columns={{ base: 2, md: 4 }} gap={4} mb={6}>
            <Box
              textAlign="center"
              p={4}
              bg={realtimeUpdates > 0 ? "green.50" : "gray.50"}
              borderRadius="md"
              transition="background-color 0.3s"
            >
              <Text fontSize="sm" color="gray.600" mb={1}>Total Extensions</Text>
              <Text fontSize="lg" fontWeight="bold">{extensionStats.totalExtensions}</Text>
            </Box>
            <Box
              textAlign="center"
              p={4}
              bg={realtimeUpdates > 0 ? "green.50" : "gray.50"}
              borderRadius="md"
              transition="background-color 0.3s"
            >
              <Text fontSize="sm" color="gray.600" mb={1}>Total Time Added</Text>
              <Text fontSize="lg" fontWeight="bold">{extensionStats.totalExtendedMinutes}m</Text>
            </Box>
            <Box
              textAlign="center"
              p={4}
              bg={realtimeUpdates > 0 ? "green.50" : "gray.50"}
              borderRadius="md"
              transition="background-color 0.3s"
            >
              <Text fontSize="sm" color="gray.600" mb={1}>Avg Trigger Bid</Text>
              <Text fontSize="lg" fontWeight="bold">${extensionStats.averageTriggerBid.toFixed(2)}</Text>
            </Box>
            <Box
              textAlign="center"
              p={4}
              bg={realtimeUpdates > 0 ? "green.50" : "gray.50"}
              borderRadius="md"
              transition="background-color 0.3s"
            >
              <Text fontSize="sm" color="gray.600" mb={1}>Highest Trigger</Text>
              <Text fontSize="lg" fontWeight="bold">${extensionStats.highestTriggerBid.toFixed(2)}</Text>
            </Box>
          </SimpleGrid>
        )}

        {open && (
          <VStack gap={4} align="stretch">
            <Separator />
            {extensionStats && extensionStats.extensionsByType.length > 0 && (
              <Box>
                <Text fontWeight="semibold" mb={2} fontSize="sm" color="gray.600">
                  Extensions by Type:
                </Text>
                <HStack gap={4}>
                  {extensionStats.extensionsByType.map((type) => (
                    <HStack key={type.triggeredBy}>
                      <Icon
                        as={type.triggeredBy === 'auto-bid' ? FaRobot : FaUser}
                        color={type.triggeredBy === 'auto-bid' ? 'purple.500' : 'blue.500'}
                        boxSize={3}
                      />
                      <Text fontSize="sm">
                        {type.triggeredBy === 'auto-bid' ? 'Auto-bid' : 'Manual'}: {type.count}
                      </Text>
                    </HStack>
                  ))}
                </HStack>
              </Box>
            )}

            <Separator />

            <VStack gap={3} align="stretch">
              <Text fontWeight="semibold" fontSize="sm" color="gray.600">
                Extension History:
              </Text>

              {extensionLogs.extensionLogs.map((log, index) => {
                // Highlight the most recent log if there was a real-time update
                const isRecentlyAdded = index === 0 && realtimeUpdates > 0;

                return (
                  <Box
                    key={log.id}
                    p={4}
                    bg={isRecentlyAdded ? "green.50" : "gray.50"}
                    borderRadius="md"
                    border="1px solid"
                    borderColor={isRecentlyAdded ? "green.200" : "gray.200"}
                    transition="all 0.3s"
                    position="relative"
                  >
                  <HStack justify="space-between" align="start" mb={2}>
                    <HStack>
                      <Icon
                        as={log.triggeredBy === 'auto-bid' ? FaRobot : FaUser}
                        color={log.triggeredBy === 'auto-bid' ? 'purple.500' : 'blue.500'}
                      />
                      <Badge
                        colorScheme={log.triggeredBy === 'auto-bid' ? 'purple' : 'blue'}
                        variant="subtle"
                      >
                        {log.triggeredBy === 'auto-bid' ? 'Auto-bid' : 'Manual'}
                      </Badge>
                    </HStack>
                    <Text fontSize="xs" color="gray.500">
                      {formatDistanceToNow(new Date(log.createdAt), { addSuffix: true })}
                    </Text>
                  </HStack>

                  <VStack align="start" gap={1}>
                    <HStack>
                      <Icon as={FaClock} boxSize={3} color="gray.500" />
                      <Text fontSize="sm">
                        Extended by <strong>{log.extendedMinutes} minutes</strong>
                      </Text>
                    </HStack>

                    <Text fontSize="xs" color="gray.600">
                      From: {format(new Date(log.previousEndDate), 'MMM dd, yyyy HH:mm')}
                    </Text>
                    <Text fontSize="xs" color="gray.600">
                      To: {format(new Date(log.newEndDate), 'MMM dd, yyyy HH:mm')}
                    </Text>

                    <Text fontSize="xs" color="gray.600">
                      Triggered by bid: <strong>${log.triggerBidAmount.toFixed(2)}</strong>
                    </Text>
                  </VStack>
                </Box>
                );
              })}
            </VStack>
          </VStack>
        )}
      </CardBody>
    </Card.Root>
  );
};

export default AuctionExtensionLogs;
