
NEXTAUTH_URL="http://*********:3000"
NEXTAUTH_SECRET="your_strong_secret_here"

GOOGLE_CLIENT_ID="509129731144-g4dph50fkhk4re75molfo4edg3l43mq6.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

NEXT_PUBLIC_API_URL="http://*********:3000/api/v1"
NEXT_PUBLIC_WS_URL="ws://*********:3001"
WS_PORT=3001

# //API ENV
# DATABASE_URL="mysql://u614718425_collectible:R1QBA;6a@************:3306/u614718425_collectible"
# DATABASE_URL="mysql://root:root@localhost/king_collectible?socket=/Applications/MAMP/tmp/mysql/mysql.sock"
DATABASE_URL="mysql://coeganehajanih:c0baka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ini@db:3306/king_collectible"
# DATABASE_URL="mysql://coeganehajanih:c0bakamutauakuadadisini@*********:3306/king_collectible"
JWT_SECRET="rahasia-sekali-sangat-rahasia-sangat-rahasia-@cobaKamuTebak"
JWT_EXPIRES_IN="7d"

# Cloudinary Configuration (for image uploads)
CLOUDINARY_CLOUD_NAME="dzis1fyo5"
CLOUDINARY_API_KEY="733121447349356"
CLOUDINARY_API_SECRET="giUUiSof8MN29OOGkSHekSkP7bk" 

XENDIT_SECRET_KEY="xnd_development_qD3IMRsaIerHekWSD0lH3xt2KYW65Zf77Mpeg1twD2qSmdco6lRklP1QbLX7V"
XENDIT_WEBHOOK_TOKEN="yEuQtK4BryjbUmjoVgII0rdXk1xfEFpozPN9bij7PQIZD6vv"
NEXT_PUBLIC_FIXER_API_KEY="********************************"
SHIPPO_API_KEY="shippo_test_cc9a558039b301faee92a73f07e3d87ca2b8fb41"
DHL_API_KEY=
DHL_API_SECRET=
FEDEX_API_KEY=
FEDEX_SECRET_KEY=
FRONTEND_URL="http://*********:3000"

SMTP_HOST=smtp.gmail.com
SMTP_PORT=465
SMTP_USER="<EMAIL>"
SMTP_PASS="dzog zetf pwsh zofd"

DB_ROOT_PASSWORD="C0b@t3b@k1966#"
DB_USER="coeganehajanih"
DB_PASSWORD="c0bakamutauakuadadisini"
DB_NAME="king_collectible"

# UPLOAD_DIR="/app/uploads"
# MAX_FILE_SIZE=10485760
# CORS_ORIGIN="http://YOUR_PUBLIC_IP"
TZ="Asia/Jakarta"
NODE_ENV="production"