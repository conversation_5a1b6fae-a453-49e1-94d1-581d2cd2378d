import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import webSocketService from "./websocket.service";
import notificationService from "./notification.service";
import { getJakartaTime } from "../utils/timezone.util";

interface AutoBidData {
  productId: string;
  startingBid: number;
  maxBudget: number;
  bidIncrement: number;
}

class AutoBidService {
  /**
   * Enable auto-bid for a user on a product
   */
  async enableAutoBid(userId: string, data: AutoBidData) {
    try {
      const { productId, startingBid, maxBudget, bidIncrement } = data;

      // Validate product exists and is an auction
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          bids: {
            orderBy: { amount: 'desc' },
            take: 1
          }
        }
         
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      if (product.sellType !== "auction") {
        return errorResponse("Auto-bid is only available for auction items");
      }

      if (product.status !== "active") {
        return errorResponse("Product is not active");
      }

      // Check auction timing
      const now = new Date();
      if (product.auctionStartDate && now < new Date(product.auctionStartDate)) {
        return errorResponse("Auction has not started yet");
      }

      if (product.auctionEndDate && now > new Date(product.auctionEndDate)) {
        return errorResponse("Auction has ended");
      }

      // Validate starting bid, max budget and increment
      const currentBid = product.currentBid ? Number(product.currentBid) : 0;

      if (startingBid <= currentBid) {
        return errorResponse(`Starting bid must be higher than current bid ($${currentBid})`);
      }

      if (maxBudget <= startingBid) {
        return errorResponse(`Max budget must be higher than starting bid ($${startingBid})`);
      }

      // Validate bid increment
      if (bidIncrement <= 0) {
        return errorResponse("Bid increment must be greater than 0");
      }

      if (bidIncrement >= (maxBudget - startingBid)) {
        return errorResponse("Bid increment is too large for the budget range");
      }

      // Check if user already has auto-bid enabled for this product
      const existingAutoBid = await prisma.autoBid.findUnique({
        where: {
          productId_bidderId: {
            productId,
            bidderId: userId
          }
        }
      });

      let autoBid;
      if (existingAutoBid) {
        // Update existing auto-bid
        autoBid = await prisma.autoBid.update({
          where: { id: existingAutoBid.id },
          data: {
            startingBid,
            maxBudget,
            bidIncrement,
            isActive: true,
            updatedAt: new Date()
          }
        });
      } else {
        // Create new auto-bid
        autoBid = await prisma.autoBid.create({
          data: {
            productId,
            bidderId: userId,
            startingBid,
            maxBudget,
            bidIncrement,
            isActive: true
          }
        });
      }

      // Place initial bid if starting bid is higher than current bid
      if (startingBid > currentBid && startingBid <= maxBudget) {
        console.log(`Placing initial auto-bid: ${startingBid} for user ${userId}`);
        await this.placeBidForUser(userId, productId, startingBid, 'auto');
      }

      // Notify user about auto-bid activation
      webSocketService.notifyAutoBidActivated(userId, productId, {
        ...autoBid,
        maxBudget: Number(autoBid.maxBudget),
        bidIncrement: Number(autoBid.bidIncrement)
      });

      return successResponse("Auto-bid enabled successfully", {
        autoBid: {
          ...autoBid,
          maxBudget: Number(autoBid.maxBudget),
          bidIncrement: Number(autoBid.bidIncrement)
        }
      });

    } catch (error) {
      console.error('Enable auto-bid error:', error);
      return errorResponse("Failed to enable auto-bid");
    }
  }

  /**
   * Disable auto-bid for a user on a product
   */
  async disableAutoBid(userId: string, productId: string) {
    try {
      const autoBid = await prisma.autoBid.findUnique({
        where: {
          productId_bidderId: {
            productId,
            bidderId: userId
          }
        }
      });

      if (!autoBid) {
        return errorResponse("Auto-bid not found");
      }

      await prisma.autoBid.update({
        where: { id: autoBid.id },
        data: { isActive: false }
      });

      return successResponse("Auto-bid disabled successfully");

    } catch (error) {
      console.error('Disable auto-bid error:', error);
      return errorResponse("Failed to disable auto-bid");
    }
  }

  /**
   * Get user's auto-bid settings for a product
   */
  async getUserAutoBid(userId: string, productId: string) {
    try {
      const autoBid = await prisma.autoBid.findUnique({
        where: {
          productId_bidderId: {
            productId,
            bidderId: userId
          }
        },
        include: {
          product: {
            select: {
              id: true,
              itemName: true,
              currentBid: true,
              auctionEndDate: true
            }
          }
        }
      });

      if (!autoBid) {
        return successResponse("No auto-bid found", null);
      }

      return successResponse("Auto-bid retrieved successfully", {
        ...autoBid,
        maxBudget: Number(autoBid.maxBudget),
        bidIncrement: Number(autoBid.bidIncrement)
      });

    } catch (error) {
      console.error('Get user auto-bid error:', error);
      return errorResponse("Failed to get auto-bid");
    }
  }

  /**
   * Process auto-bids when a new bid is placed
   */
  async processAutoBids(productId: string, newBidAmount: number, excludeBidderId?: string) {
    try {
      console.log(`Processing auto-bids for product ${productId}, new bid: ${newBidAmount}, exclude: ${excludeBidderId}`);

      // Get all active auto-bids for this product (excluding the bidder who just placed a bid)
      const autoBids = await prisma.autoBid.findMany({
        where: {
          productId,
          isActive: true,
          ...(excludeBidderId && { bidderId: { not: excludeBidderId } })
        },
        include: {
          bidder: {
            select: { id: true, firstName: true, lastName: true }
          }
        },
        orderBy: { maxBudget: 'desc' } // Process highest budget first
      });

      console.log(`Found ${autoBids.length} active auto-bids to process`);

      let currentHighestBid = newBidAmount;
      let processedBids = 0;

      // Process auto-bids in order of highest budget first
      for (const autoBid of autoBids) {
        // TODO: Add startingBid after migration
        const startingBid = currentHighestBid + Number(autoBid.bidIncrement);
        const maxBudget = Number(autoBid.maxBudget);
        const bidIncrement = Number(autoBid.bidIncrement);

        console.log(`Processing auto-bid for user ${autoBid.bidderId}: startingBid=${startingBid}, maxBudget=${maxBudget}, increment=${bidIncrement}`);

        // Calculate next bid amount (use starting bid if current bid is lower, otherwise increment)
        const nextBidAmount = Math.max(startingBid, currentHighestBid + bidIncrement);

        // Check if auto-bid budget allows this bid
        if (nextBidAmount <= maxBudget) {
          console.log(`Placing auto-bid: ${nextBidAmount} for user ${autoBid.bidderId}`);

          // Place auto-bid
          try {
            const bidResult = await this.placeBidForUser(autoBid.bidderId, productId, nextBidAmount, 'auto');

            if (bidResult) {
              // Update the current highest bid for next iteration
              currentHighestBid = nextBidAmount;
              processedBids++;

              console.log(`✅ Auto-bid placed successfully: ${nextBidAmount}`);

              // Notify user about auto-bid execution
              webSocketService.notifyAutoBidExecuted(autoBid.bidderId, productId, bidResult);

              // Add small delay to prevent race conditions
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          } catch (error) {
            console.log(`❌ Failed to place auto-bid: ${error}`);
          }
        } else {
          console.log(`Auto-bid budget exceeded: ${nextBidAmount} > ${maxBudget}`);
        }
      }

      console.log(`Auto-bid processing completed. Processed ${processedBids} bids.`);

      return successResponse("Auto-bids processed successfully", {
        processedBids,
        finalHighestBid: currentHighestBid
      });

    } catch (error) {
      console.error('Process auto-bids error:', error);
      return errorResponse("Failed to process auto-bids");
    }
  }

  /**
   * Place a bid for a user (used by auto-bid system)
   */
  private async placeBidForUser(bidderId: string, productId: string, bidAmount: number, bidType: 'manual' | 'auto' = 'auto') {
    try {
      // Create bid in transaction
      const result = await prisma.$transaction(async (tx) => {
        // Mark all previous bids as not winning
        await tx.bid.updateMany({
          where: { productId },
          data: { isWinning: false },
        });

        // Create new bid
        const bid = await tx.bid.create({
          data: {
            productId,
            bidderId,
            amount: bidAmount,
            bidType,
            isWinning: true,
          },
        });

        // Update product with new current bid and bid count
        await tx.product.update({
          where: { id: productId },
          data: {
            currentBid: bidAmount,
            bidCount: { increment: 1 },
          },
        });

        return bid;
      });

      // Handle extend bidding for auto-bids
      try {
        const product = await prisma.product.findUnique({
          where: { id: productId }
        });

        if (product) {
          console.log(`🤖 Starting auto-bid extend bidding check for product ${product.id}`);
          console.log(`📊 Auto-bid product extend bidding settings:`, {
            extendedBiddingEnabled: product.extendedBiddingEnabled,
            extendedBiddingMinutes: product.extendedBiddingMinutes,
            extendedBiddingDuration: product.extendedBiddingDuration,
            auctionEndDate: product.auctionEndDate
          });

          const extensionResult = await this.handleExtendBidding(product, bidAmount, bidderId);
          console.log('🎯 Auto-bid extension result:', extensionResult);

          if (extensionResult.extended) {
            console.log(`✅ AUTO-BID AUCTION EXTENDED! New end date: ${extensionResult.newEndDate}`);
          } else {
            console.log(`❌ Auto-bid no extension: ${extensionResult.reason || 'Unknown reason'}`);
          }
        }
      } catch (error) {
        console.error('❌ Extend bidding processing error in auto-bid:', error);
        // Don't fail the auto-bid if extend bidding fails
      }

      return result;

    } catch (error) {
      console.error('Place bid for user error:', error);
      throw error;
    }
  }

  /**
   * Get all auto-bids for a user
   */
  async getUserAutoBids(userId: string) {
    try {
      const autoBids = await prisma.autoBid.findMany({
        where: { 
          bidderId: userId,
          isActive: true
        },
        include: {
          product: {
            select: {
              id: true,
              itemName: true,
              slug: true,
              currentBid: true,
              auctionEndDate: true,
              status: true,
              images: {
                where: { isMain: true },
                take: 1
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      const transformedAutoBids = autoBids.map(autoBid => ({
        ...autoBid,
        maxBudget: Number(autoBid.maxBudget),
        bidIncrement: Number(autoBid.bidIncrement),
        product: {
          ...autoBid.product,
          currentBid: autoBid.product.currentBid ? Number(autoBid.product.currentBid) : null
        }
      }));

      return successResponse("User auto-bids retrieved successfully", transformedAutoBids);

    } catch (error) {
      console.error('Get user auto-bids error:', error);
      return errorResponse("Failed to get user auto-bids");
    }
  }

  /**
   * Handle extend bidding logic when an auto-bid is placed
   */
  async handleExtendBidding(product: any, bidAmount: number, bidderId?: string) {
    // Check if extend bidding is enabled for this product
    if (!product.extendedBiddingEnabled || !product.extendedBiddingMinutes || !product.extendedBiddingDuration) {
      console.log(`Auto-bid extend bidding not enabled for product ${product.id}`);
      return {
        extended: false,
        reason: 'Extended bidding not enabled'
      };
    }

    // Use Jakarta time for consistency with auction scheduler
    const now = getJakartaTime();
    const auctionEndDate = new Date(product.auctionEndDate);

    // Calculate time remaining until auction ends (in minutes)
    const timeRemainingMs = auctionEndDate.getTime() - now.getTime();
    const timeRemainingMinutes = Math.floor(timeRemainingMs / (1000 * 60));

    console.log(`🤖 Auto-bid extend bidding check for product ${product.id}:`);
    console.log(`  - Current time (Jakarta): ${now.toISOString()}`);
    console.log(`  - Auction end time: ${auctionEndDate.toISOString()}`);
    console.log(`  - Time remaining: ${timeRemainingMinutes} minutes`);
    console.log(`  - Trigger threshold: ${product.extendedBiddingMinutes} minutes`);

    // Check if we're within the extend bidding trigger window
    if (timeRemainingMinutes <= product.extendedBiddingMinutes && timeRemainingMinutes > 0) {
      console.log(`🚀 Auto-bid triggering auction extension for product ${product.id}`);

      // Extend the auction by the specified duration
      const newEndDate = new Date(auctionEndDate.getTime() + (product.extendedBiddingDuration * 60 * 1000));

      console.log(`📅 Auto-bid extending auction from ${auctionEndDate.toISOString()} to ${newEndDate.toISOString()}`);

      // Use transaction to ensure atomicity
      const result = await prisma.$transaction(async (tx) => {
        // Update the product's auction end date with optimistic locking to prevent race conditions
        const updatedProduct = await tx.product.update({
          where: {
            id: product.id,
            // Only update if the auction end date hasn't changed (prevents double extensions)
            auctionEndDate: product.auctionEndDate
          },
          data: {
            auctionEndDate: newEndDate
          }
        });

        if (!updatedProduct) {
          throw new Error('Auction end date was already modified by another process');
        }

        // Create extension log
        const extensionLog = await tx.auctionExtensionLog.create({
          data: {
            productId: product.id,
            previousEndDate: auctionEndDate,
            newEndDate: newEndDate,
            extendedMinutes: product.extendedBiddingDuration,
            triggerBidAmount: bidAmount,
            triggeredBy: 'auto-bid',
            triggeredBidderId: bidderId,
            extensionReason: 'bid_in_final_minutes'
          }
        });

        return { updatedProduct, extensionLog };
      });

      if (!result) {
        console.log(`⚠️ Auto-bid auction extension skipped for product ${product.id} - auction end date was already modified`);
        return {
          extended: false,
          reason: 'Already extended by another process'
        };
      }

      // Send WebSocket notification about auction extension
      try {
        webSocketService.notifyAuctionExtended(product.id, {
          productId: product.id,
          newEndDate: newEndDate.toISOString(),
          extendedMinutes: product.extendedBiddingDuration,
          triggerBidAmount: bidAmount,
          triggeredBy: 'auto-bid',
          triggeredAt: now.toISOString(),
          previousEndDate: auctionEndDate.toISOString(),
          extensionLogId: result.extensionLog.id
        });

        // Also send notification to all bidders
        await notificationService.sendAuctionExtensionNotification(product.id, {
          productId: product.id,
          newEndDate: newEndDate.toISOString(),
          extendedMinutes: product.extendedBiddingDuration,
          triggerBidAmount: bidAmount,
          triggeredBy: 'auto-bid',
          triggeredAt: now.toISOString(),
          previousEndDate: auctionEndDate.toISOString(),
          extensionLogId: result.extensionLog.id
        });
      } catch (error) {
        console.error('❌ Auto-bid WebSocket/notification auction extension error:', error);
      }

      console.log(`✅ Auto-bid auction extended by ${product.extendedBiddingDuration} minutes due to auto-bid in final ${product.extendedBiddingMinutes} minutes`);
      console.log(`📝 Auto-bid extension logged with ID: ${result.extensionLog.id}`);

      return {
        extended: true,
        newEndDate: newEndDate.toISOString(),
        previousEndDate: auctionEndDate.toISOString(),
        extendedMinutes: product.extendedBiddingDuration,
        extensionLogId: result.extensionLog.id
      };
    } else {
      console.log(`⏰ Auto-bid no extension needed: ${timeRemainingMinutes} minutes remaining (threshold: ${product.extendedBiddingMinutes})`);
      return {
        extended: false,
        timeRemaining: timeRemainingMinutes
      };
    }
  }
}

const autoBidService = new AutoBidService();
export default autoBidService;
